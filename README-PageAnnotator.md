# PageAnnotator React Component

This document describes the PageAnnotator React component and the build system for compiling JSX to JavaScript.

## Overview

The PageAnnotator component is a React wrapper around LiterallyCanvas that provides annotation functionality for e-book pages. It has been migrated from a modal-based implementation to a React component-based approach.

## Files

### Source Files
- `ebookshelf/js/PageAnnotator.jsx` - React component source (JSX)
- `ebookshelf/js/page-annotator.js` - Compiled JavaScript version

### Build Files
- `package.json` - Node.js dependencies and build scripts
- `compile-page-annotator.js` - Compilation script using Babel

### CSS Changes
- `ebookshelf/css/magazine.css` - Simplified CSS with old LiterallyCanvas styles removed and new #page-annotator styles added

## Changes Made

### 1. CSS Simplification
Removed old LiterallyCanvas styles from `ebookshelf/css/magazine.css`:
- `.lc-pen` styles (lines 799-807)
- `#lc-canvas` styles (lines 809-815)
- Pen tool styles (`#pen1`, `#pen2`, `#pen3`, `#eraser`)
- `.lc-btn` styles and button-specific styles

Added new styles for the React component:
```css
/* Page Annotator Container */
#page-annotator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    pointer-events: none; /* Allow clicks to pass through when not active */
}

#page-annotator.active {
    pointer-events: auto; /* Enable interactions when active */
}

/* Ensure the React component container fills the space */
#page-annotator .literally-canvas-container {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
}
```

### 2. Asset Group Configuration
The `min/groupsConfig.php` already includes the necessary files in the `ebookshelf-js` group:
- React production libraries
- LiterallyCanvas libraries
- PageAnnotator component (`page-annotator.js`)

### 3. Build System
Created a modern build system using Babel:
- `npm install` - Install dependencies
- `npm run compile-annotator` - Compile JSX to JavaScript
- `npm run build` - Alias for compile-annotator

## Usage

### Development Workflow
1. Edit `ebookshelf/js/PageAnnotator.jsx` for component changes
2. Run `npm run compile-annotator` to compile to `page-annotator.js`
3. The compiled file is automatically included via the asset groups

### Component Usage
The PageAnnotator component is used in `ebookshelf/js/loadapp.js`:

```javascript
var annotatorProps = {
    width: flipBookSingleWidth,
    height: flipBookHeight,
    tools: [LC.tools.Pencil, LC.tools.Eraser, LC.tools.Line],
    primaryColor: 'hsla(60, 100%, 50%, 0.5)',
    strokeWidths: [3, 15, 25],
    defaultStrokeWidth: 15,
    snapshot: existingSnapshot,
    onDrawingChange: function() {
        savePageAnnotatorData();
    },
    onInit: function(lc) {
        pageAnnotatorRef = lc;
        lc.setTool(new LC.tools.Line(lc));
        lc.setColor('primary', 'hsla(60, 100%, 50%, 0.5)');
        lc.setZoom(winHeight / flipBookHeight);
    }
};

// Mount React component
ReactDOM.render(React.createElement(PageAnnotator, annotatorProps), pageAnnotatorContainer);
```

## Dependencies

### Runtime Dependencies
- React 16+ (included in asset groups)
- ReactDOM (included in asset groups)
- LiterallyCanvas (included in asset groups)

### Build Dependencies
- @babel/core
- @babel/preset-env
- @babel/preset-react

## Notes

- The compiled JavaScript uses `React.createElement` for compatibility with older browsers
- The component is available globally as `window.PageAnnotator`
- CSS positioning ensures the annotator overlays the page content properly
- The build system is optional - the current compiled version is already functional
