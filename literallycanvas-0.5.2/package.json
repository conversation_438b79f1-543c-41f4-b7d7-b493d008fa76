{"name": "<PERSON><PERSON><PERSON>", "version": "0.5.2", "description": "HTML5 drawing widget", "main": "lib/js", "scripts": {"prepublish": "gulp commonjs", "test": "echo \"Error: no test specified\" && exit 1", "dev": "gulp dev", "gulp": "gulp"}, "repository": {"type": "git", "url": "git://github.com/literallycanvas/literallycanvas.git"}, "keywords": ["html5", "canvas", "drawing"], "author": "<PERSON> and <PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/literallycanvas/literallycanvas/issues"}, "devDependencies": {"babel-preset-es2015": "^6.3.13", "babel-preset-react": "^6.3.13", "babelify": "^7.2.0", "browserify": "~13.0.0", "coffeeify": "^2.0.1", "create-react-class": "^15.6.2", "envify": "~3.4.0", "gulp": "~3.9.0", "gulp-babel": "^6.1.1", "gulp-coffee": "^2.3.1", "gulp-connect": "~2.3.1", "gulp-preprocess": "^2.0.0", "gulp-rename": "~1.2.2", "gulp-sass": "^2.1.1", "gulp-streamify": "1.0.2", "gulp-uglify": "~1.5.1", "merge-stream": "^1.0.0", "preprocessify": "0.0.6", "react": "^16.0.0", "react-dom": "^16.0.0", "react-dom-factories": "^1.0.2", "streamify": "~0.2.5", "uglify-js": "~2.6.1", "vinyl-source-stream": "~1.1.0", "watchify": "~3.7.0"}, "dependencies": {"react-addons-pure-render-mixin": "^15.1"}, "peerDependencies": {"create-react-class": "^15.6.2", "react": "^0.14.6 || ^15.1 || ^16.0.0", "react-dom": "^0.14.6 || ^15.1 || ^16.0.0", "react-dom-factories": "^1.0.2"}}