// LiterallyCanvas with React Support
// This file combines the core LiterallyCanvas functionality with React components

// First, load the core LiterallyCanvas library
// The core library is already loaded via literallycanvas-core.js

// Extend LC with React components
(function() {
  'use strict';
  
  // Ensure LC is available
  if (typeof LC === 'undefined') {
    console.error('LiterallyCanvas core library not found. Make sure literallycanvas-core.js is loaded first.');
    return;
  }

  // React Component for LiterallyCanvas
  const LiterallyCanvasReact = (props) => {
    const {
      width = 800,
      height = 600,
      tools = null,
      onDrawingChange = null,
      snapshot = null,
      backgroundShapes = [],
      primaryColor = '#000',
      secondaryColor = '#fff',
      backgroundColor = 'transparent',
      strokeWidths = [1, 2, 5, 10, 20],
      defaultStrokeWidth = 2,
      imageSize = null,
      toolbarPosition = 'top',
      onInit = null,
      className = '',
      style = {}
    } = props;

    const containerRef = React.useRef(null);
    const lcRef = React.useRef(null);
    const [isInitialized, setIsInitialized] = React.useState(false);

    // Default tools if none provided
    const defaultTools = [
      LC.tools.Pencil,
      LC.tools.Eraser,
      LC.tools.Line,
      LC.tools.Rectangle,
      LC.tools.Ellipse,
      LC.tools.Text,
      LC.tools.Polygon,
      LC.tools.Pan,
      LC.tools.Eyedropper
    ];

    React.useEffect(() => {
      if (!containerRef.current || isInitialized) return;

      // Initialize LiterallyCanvas
      const lcOptions = {
        imageSize: imageSize || { width: width, height: height },
        tools: tools || defaultTools,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        backgroundColor: backgroundColor,
        strokeWidths: strokeWidths,
        defaultStrokeWidth: defaultStrokeWidth,
        backgroundShapes: backgroundShapes,
        snapshot: snapshot,
        toolbarPosition: toolbarPosition
      };

      // Create LiterallyCanvas instance
      const lc = LC.init(containerRef.current, lcOptions);
      lcRef.current = lc;

      // Set up event listeners
      if (onDrawingChange) {
        lc.on('drawingChange', onDrawingChange);
      }

      if (onInit) {
        onInit(lc);
      }

      setIsInitialized(true);

      // Cleanup function
      return () => {
        if (lcRef.current) {
          lcRef.current.teardown();
          lcRef.current = null;
        }
        setIsInitialized(false);
      };
    }, []);

    // Update colors when props change
    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('primary', primaryColor);
      }
    }, [primaryColor]);

    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('secondary', secondaryColor);
      }
    }, [secondaryColor]);

    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('background', backgroundColor);
      }
    }, [backgroundColor]);

    // Expose LC instance methods
    const getSnapshot = () => {
      return lcRef.current ? lcRef.current.getSnapshot() : null;
    };

    const loadSnapshot = (snapshot) => {
      if (lcRef.current) {
        lcRef.current.loadSnapshot(snapshot);
      }
    };

    const clear = () => {
      if (lcRef.current) {
        lcRef.current.clear();
      }
    };

    const undo = () => {
      if (lcRef.current) {
        lcRef.current.undo();
      }
    };

    const redo = () => {
      if (lcRef.current) {
        lcRef.current.redo();
      }
    };

    const setTool = (tool) => {
      if (lcRef.current) {
        lcRef.current.setTool(new tool(lcRef.current));
      }
    };

    const setZoom = (scale) => {
      if (lcRef.current) {
        lcRef.current.setZoom(scale);
      }
    };

    const setPan = (x, y) => {
      if (lcRef.current) {
        lcRef.current.setPan(x, y);
      }
    };

    const getImage = (options = {}) => {
      return lcRef.current ? lcRef.current.getImage(options) : null;
    };

    const getSVGString = () => {
      return lcRef.current ? lcRef.current.getSVGString() : '';
    };

    // Expose methods via ref
    React.useImperativeHandle(React.forwardRef(() => {}), () => ({
      getSnapshot,
      loadSnapshot,
      clear,
      undo,
      redo,
      setTool,
      setZoom,
      setPan,
      getImage,
      getSVGString,
      lc: lcRef.current
    }));

    const containerStyle = {
      width: width + 'px',
      height: height + 'px',
      border: '1px solid #ccc',
      position: 'relative',
      ...style
    };

    return React.createElement('div', {
      ref: containerRef,
      style: containerStyle,
      className: 'literally-canvas-container ' + className
    });
  };

  // Add React component to LC namespace
  LC.LiterallyCanvasReact = LiterallyCanvasReact;

  // Also make it available as a standalone component
  if (typeof window !== 'undefined') {
    window.LiterallyCanvasReact = LiterallyCanvasReact;
  }

  // Helper function to create shapes
  LC.createShape = LC.createShape || function(shapeType, options) {
    return LC.shapes.createShape(shapeType, options);
  };

})();
