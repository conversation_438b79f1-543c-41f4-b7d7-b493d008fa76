import React, { useEffect, useRef, useState } from 'react';

// PageAnnotator React Component
// This component wraps LiterallyCanvas core functionality in a React component
const PageAnnotator = ({ 
  width = 800, 
  height = 600, 
  tools = null,
  onDrawingChange = null,
  snapshot = null,
  backgroundShapes = [],
  primaryColor = '#000',
  secondaryColor = '#fff',
  backgroundColor = 'transparent',
  strokeWidths = [1, 2, 5, 10, 20],
  defaultStrokeWidth = 2,
  imageSize = null,
  toolbarPosition = 'top',
  onInit = null
}) => {
  const containerRef = useRef(null);
  const lcRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Default tools if none provided
  const defaultTools = [
    LC.tools.Pencil,
    LC.tools.Eraser,
    LC.tools.Line,
    LC.tools.Rectangle,
    LC.tools.Ellipse,
    LC.tools.Text,
    LC.tools.Polygon,
    LC.tools.Pan,
    LC.tools.Eyedropper
  ];

  useEffect(() => {
    if (!containerRef.current || isInitialized) return;

    // Initialize LiterallyCanvas
    const lcOptions = {
      imageSize: imageSize || { width: width, height: height },
      tools: tools || defaultTools,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      strokeWidths: strokeWidths,
      defaultStrokeWidth: defaultStrokeWidth,
      backgroundShapes: backgroundShapes,
      snapshot: snapshot
    };

    // Create LiterallyCanvas instance
    const lc = LC.init(containerRef.current, lcOptions);
    lcRef.current = lc;

    // Set up event listeners
    if (onDrawingChange) {
      lc.on('drawingChange', onDrawingChange);
    }

    if (onInit) {
      onInit(lc);
    }

    setIsInitialized(true);

    // Cleanup function
    return () => {
      if (lcRef.current) {
        lcRef.current.teardown();
        lcRef.current = null;
      }
      setIsInitialized(false);
    };
  }, []);

  // Update colors when props change
  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('primary', primaryColor);
    }
  }, [primaryColor]);

  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('secondary', secondaryColor);
    }
  }, [secondaryColor]);

  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('background', backgroundColor);
    }
  }, [backgroundColor]);

  // Expose LC instance methods
  const getSnapshot = () => {
    return lcRef.current ? lcRef.current.getSnapshot() : null;
  };

  const loadSnapshot = (snapshot) => {
    if (lcRef.current) {
      lcRef.current.loadSnapshot(snapshot);
    }
  };

  const clear = () => {
    if (lcRef.current) {
      lcRef.current.clear();
    }
  };

  const undo = () => {
    if (lcRef.current) {
      lcRef.current.undo();
    }
  };

  const redo = () => {
    if (lcRef.current) {
      lcRef.current.redo();
    }
  };

  const setTool = (tool) => {
    if (lcRef.current) {
      lcRef.current.setTool(new tool(lcRef.current));
    }
  };

  const setZoom = (scale) => {
    if (lcRef.current) {
      lcRef.current.setZoom(scale);
    }
  };

  const setPan = (x, y) => {
    if (lcRef.current) {
      lcRef.current.setPan(x, y);
    }
  };

  const getImage = (options = {}) => {
    return lcRef.current ? lcRef.current.getImage(options) : null;
  };

  const getSVGString = () => {
    return lcRef.current ? lcRef.current.getSVGString() : '';
  };

  // Expose methods via ref
  React.useImperativeHandle(React.forwardRef(() => {}), () => ({
    getSnapshot,
    loadSnapshot,
    clear,
    undo,
    redo,
    setTool,
    setZoom,
    setPan,
    getImage,
    getSVGString,
    lc: lcRef.current
  }));

  return (
    <div style={{ position: 'relative', width: width + 'px', height: height + 'px' }}>
      {/* LiterallyCanvas Container */}
      <div
        ref={containerRef}
        style={{
          width: width + 'px',
          height: height + 'px',
          border: '1px solid #ccc',
          position: 'relative'
        }}
        className="literally-canvas-container"
      />

      {/* Custom Toolbar */}
      <div className="page-annotator-toolbar" style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        display: 'flex',
        flexDirection: 'column',
        gap: '5px',
        zIndex: 1000
      }}>
        {/* Pen Tools */}
        <button
          className="annotator-tool-btn pen1-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.setTool(new LC.tools.Pencil(lcRef.current));
              lcRef.current.setColor('primary', '#ff0000');
              lcRef.current.setStrokeWidth(3);
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/pen1.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="红笔"
        />

        <button
          className="annotator-tool-btn pen2-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.setTool(new LC.tools.Pencil(lcRef.current));
              lcRef.current.setColor('primary', '#0000ff');
              lcRef.current.setStrokeWidth(3);
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/pen2.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="蓝笔"
        />

        <button
          className="annotator-tool-btn pen3-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.setTool(new LC.tools.Pencil(lcRef.current));
              lcRef.current.setColor('primary', '#00ff00');
              lcRef.current.setStrokeWidth(3);
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/pen3.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="绿笔"
        />

        {/* Eraser */}
        <button
          className="annotator-tool-btn eraser-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.setTool(new LC.tools.Eraser(lcRef.current));
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/eraser.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="橡皮擦"
        />

        {/* Undo */}
        <button
          className="annotator-tool-btn undo-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.undo();
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/undo.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="撤销"
        />

        {/* Redo */}
        <button
          className="annotator-tool-btn redo-btn"
          onClick={() => {
            if (lcRef.current) {
              lcRef.current.redo();
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/redo.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="重做"
        />

        {/* Zoom In */}
        <button
          className="annotator-tool-btn zoomin-btn"
          onClick={() => {
            if (lcRef.current) {
              const currentZoom = lcRef.current.getZoom();
              lcRef.current.setZoom(Math.min(currentZoom * 1.2, 3));
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            fontSize: '20px',
            fontWeight: 'bold'
          }}
          title="放大"
        >
          +
        </button>

        {/* Zoom Out */}
        <button
          className="annotator-tool-btn zoomout-btn"
          onClick={() => {
            if (lcRef.current) {
              const currentZoom = lcRef.current.getZoom();
              lcRef.current.setZoom(Math.max(currentZoom / 1.2, 0.5));
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            fontSize: '20px',
            fontWeight: 'bold'
          }}
          title="缩小"
        >
          -
        </button>

        {/* Clear */}
        <button
          className="annotator-tool-btn clear-btn"
          onClick={() => {
            if (lcRef.current && confirm('确定要清除所有绘画内容吗？')) {
              lcRef.current.clear();
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundImage: 'url(../pics/clear.jpg)',
            backgroundSize: 'cover',
            border: 'none',
            cursor: 'pointer'
          }}
          title="清除"
        />

        {/* Exit Button */}
        <button
          className="annotator-tool-btn exit-btn"
          onClick={() => {
            if (window.closePageAnnotator) {
              window.closePageAnnotator();
            }
          }}
          style={{
            width: '50px',
            height: '50px',
            backgroundColor: '#ff4444',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            fontSize: '24px',
            fontWeight: 'bold'
          }}
          title="退出"
        >
          ✕
        </button>
      </div>
    </div>
  );
};

export default PageAnnotator;
