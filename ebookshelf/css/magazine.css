﻿*:focus {
    outline: none;
}

.composer-img, .instrument-img {
	float:left;
	max-height:150px;
	max-width:150px;
	margin-right:10px;
}

.ui-widget-content {
	background: Cornsilk;
}

.ui-dialog .ui-dialog-title {
	width : 300px;
	display : inline-block;
	white-space: nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}

#dialogNotes p {
	font-size: 1.2em;
}

html, body {
	user-select: none;
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}
body{
	overflow:hidden;
	background-color:#fcfcfc;
	margin:0;
	padding:0;
}

#testTable table, #testTable3 table {
	width:100%;
	border-collapse: collapse;
}

#testTable table,#testTable  th,#testTable  td,#testTable3 table,#testTable3  th,#testTable3  td {
    border: 1px solid black;
}

#testTable  td, #testTable3  td {
     text-align: center;
}

a.testHtml5:hover {
	color:orange;
	cursor:pointer;
}

a.test2:hover {
	color:orange;
	cursor:pointer;
}

a.test3:hover {
	color:orange;
	cursor:pointer;
}

.magazine-viewport .container{
	position:absolute;
	top:50%;
	left:50%;
	width:1206px;
	height:780px;
	margin:auto;
}

.magazine-viewport .magazine{
	width:1206px;
	height:780px;
	left:-603px;
	top:-300px;
}

.magazine-viewport .page{
	width:603px;
	height:780px;
	background-color:white;
	background-repeat:no-repeat;
	background-size:100% 100%;
}

.magazine-viewport .zoomer .region{
	display:none;
}

.magazine .custom-note{
	position:absolute;
	overflow:hidden;
	background-size: 100% 100%;
	cursor:pointer;
}

.magazine .custom-draw{
	position:absolute;
	background-size: 100% 100%;	
}

html.no-touch .magazine .custom-note:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region{
	position:absolute;
	overflow:hidden;
	background:#0066FF;
	opacity:0.2;
	-webkit-border-radius:10px;
	-moz-border-radius:10px;
	-ms-border-radius:10px;
	-o-border-radius:10px;
	border-radius:10px;
	cursor:pointer;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)"; */
	filter: alpha(opacity=20);
}

html.no-touch .magazine .region:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region.cover{
	background:orange;
	opacity:1;
	-ms-filter: none;
	filter: none;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
}

html.no-touch .magazine .region.cover:hover{
	opacity:1;
	-ms-filter: brightness(75%);
	filter: brightness(75%);
}

.magazine .region.answer{
	background-color: transparent;
	opacity: 1;
	-ms-filter: none;
	filter: none;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
}

html.no-touch .magazine .region.answer:hover{
	background-color: black;
	opacity: 0.1;
}

html.no-touch .magazine .region.answer2:hover{
	background-color: transparent;
	opacity: 1;
	-ms-filter: brightness(90%);
	filter: brightness(90%);
}

.magazine .answer img{
	height:100%;
	width:100%;
	display:none;
	object-fit: contain;
}

.magazine .region.audio{
	background:none;
	background-size: 100% 100%;
	opacity:1;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
	-ms-filter: none;
	filter: none;

}

html.no-touch  .magazine .region.audio:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region.audio2:hover{
	opacity:0.1;
	background-color:grey;
}

.magazine .region.notes{
	background:none;
	background-size: 100% 100%;
	opacity:1;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
	-ms-filter: none;
	filter: none;

}

html.no-touch .magazine .region.notes:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region.video{
	background:none;
	background-size: 100% 100%;
	opacity:1;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
	-ms-filter: none;
	filter: none;

}

html.no-touch .magazine .region.video:hover{
	opacity:0.5;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
	filter: alpha(opacity=50);
}

.magazine .region.html5{
	background:none;
	background-size: 100% 100%;
	opacity:1;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
	-ms-filter: none;
	filter: none;

}

html.no-touch .magazine .region.html5:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region.youtube{
	background:none;
	background-size: 100% 100%;
	opacity:1;
	-webkit-border-radius:0px;
	-moz-border-radius:0px;
	-ms-border-radius:0px;
	-o-border-radius:0px;
	border-radius:0px;
	-ms-filter: none;
	filter: none;

}

html.no-touch .magazine .region.youtube:hover{
	opacity:0.5;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; */
	filter: alpha(opacity=50);
}

.magazine .region.zoom{
	opacity:0.01;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=1)"; */
	filter: alpha(opacity=1);
}

html.no-touch .magazine .region.zoom:hover{
	opacity:0.2;
	/* -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)"; */
	filter: alpha(opacity=20);
}

.magazine .page{
	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-moz-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-ms-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-o-box-shadow:0 0 20px rgba(0,0,0,0.2);
	box-shadow:0 0 20px rgba(0,0,0,0.2);
}

.magazine-viewport .page img{
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	margin:0;
}

.magazine .even .gradient{
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;

	background:-webkit-gradient(linear, left top, right top, color-stop(0.95, rgba(0,0,0,0)), color-stop(1, rgba(0,0,0,0.2)));
	background-image:-webkit-linear-gradient(left, rgba(0,0,0,0) 95%, rgba(0,0,0,0.2) 100%);
	background-image:-moz-linear-gradient(left, rgba(0,0,0,0) 95%, rgba(0,0,0,0.2) 100%);
	background-image:-ms-linear-gradient(left, rgba(0,0,0,0) 95%, rgba(0,0,0,0.2) 100%);
	background-image:-o-linear-gradient(left, rgba(0,0,0,0) 95%, rgba(0,0,0,0.2) 100%);
	background-image:linear-gradient(left, rgba(0,0,0,0) 95%, rgba(0,0,0,0.2) 100%);
}

.magazine .odd .gradient{
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;

	background:-webkit-gradient(linear, right top, left top, color-stop(0.95, rgba(0,0,0,0)), color-stop(1, rgba(0,0,0,0.15)));
	background-image:-webkit-linear-gradient(right, rgba(0,0,0,0) 95%, rgba(0,0,0,0.15) 100%);
	background-image:-moz-linear-gradient(right, rgba(0,0,0,0) 95%, rgba(0,0,0,0.15) 100%);
	background-image:-ms-linear-gradient(right, rgba(0,0,0,0) 95%, rgba(0,0,0,0.15) 100%);
	background-image:-o-linear-gradient(right, rgba(0,0,0,0) 95%, rgba(0,0,0,0.15) 100%);
	background-image:linear-gradient(right, rgba(0,0,0,0) 95%, rgba(0,0,0,0.15) 100%);
}

.magazine-viewport .zoom-in .even .gradient,
.magazine-viewport .zoom-in .odd .gradient{

	display:none;

}

.magazine-viewport .loader{
	background-image:url(../pics/loader.gif);
	width:22px;
	height:22px;
	position:absolute;
	top:280px;
	left:219px;
}

.magazine-viewport .shadow{
	-webkit-transition: -webkit-box-shadow 0.5s;
	-moz-transition: -moz-box-shadow 0.5s;
	-o-transition: -webkit-box-shadow 0.5s;
	-ms-transition: -ms-box-shadow 0.5s;

	-webkit-box-shadow:0 0 20px #ccc;
	-moz-box-shadow:0 0 20px #ccc;
	-o-box-shadow:0 0 20px #ccc;
	-ms-box-shadow:0 0 20px #ccc;
	box-shadow:0 0 20px #ccc;
}

.magazine-viewport .next-button,
.magazine-viewport .previous-button{
	width:102px;
	height:780px;
	position:absolute;
	top:0;
}

.magazine-viewport .next-button{
	right:-102px;
	-webkit-border-radius:0 15px 15px 0;
	-moz-border-radius:0 15px 15px 0;
	-ms-border-radius:0 15px 15px 0;
	-o-border-radius:0 15px 15px 0;
	border-radius:0 15px 15px 0;
}

.magazine-viewport .previous-button{
	left:-102px;
	-webkit-border-radius:15px 0 0 15px;
	-moz-border-radius:15px 0 0 15px;
	-ms-border-radius:15px 0 0 15px;
	-o-border-radius:15px 0 0 15px;
	border-radius:15px 0 0 15px;
}

.magazine-viewport .previous-button-hover,
.magazine-viewport .next-button-hover{
	background-color:rgba(0,0,0, 0.2);
}

.magazine-viewport .previous-button-hover,
.magazine-viewport .previous-button-down{
	background-image:url(../pics/arrows.png);
	background-position:76px 334px;
	background-repeat:no-repeat;
}

.magazine-viewport .previous-button-down,
.magazine-viewport .next-button-down{
	background-color:rgba(0,0,0, 0.4);
}

.magazine-viewport .next-button-hover,
.magazine-viewport .next-button-down{
	background-image:url(../pics/arrows.png);
	background-position:-38px 334px;
	background-repeat:no-repeat;
}

.magazine-viewport .zoom-in .next-button,
.magazine-viewport .zoom-in .previous-button{
	display:none;
}

.animated{
	-webkit-transition:margin-left 0.5s;
	-moz-transition:margin-left 0.5s;
	-ms-transition:margin-left 0.5s;
	-o-transition:margin-left 0.5s;
	transition:margin-left 0.5s;
}

.thumbnail span{
   color: black;
   text-shadow: -1px -1px 0 #FFF, 1px -1px 0 #FFF, -1px 1px 0 #FFF, 1px 1px 0 #FFF;
   margin: 5px;
}

.thumbnails{
	position:absolute;
	bottom:0;
	left:0;
	width:100%;
	height:140px;
	z-index:1;
}

.thumbnails > div{
	width:1050px;
	height:100px;
	margin:20px auto;
}

.thumbnails ul{
	margin:0;
	padding:0;
	text-align:center;
	white-space: nowrap;

	-webkit-transform:scale3d(0.5, 0.5, 1);
	-moz-transform:scale3d(0.5, 0.5, 1);
	-o-transform:scale3d(0.5, 0.5, 1);
	-ms-transform:scale3d(0.5, 0.5, 1);
	transform:scale3d(0.5, 0.5, 1);
	-webkit-transition:-webkit-transform ease-in-out 100ms;
	-moz-transition:-moz-transform ease-in-out 100ms;
	-ms-transition:-ms-transform ease-in-out 100ms;
	-o-transition:-o-transform ease-in-out 100ms;
	transition:transform ease-in-out 100ms;
}

.thumbanils-touch ul{
	-webkit-transform:none;
	-moz-transform:none;
	-o-transform:none;
	-ms-transform:none;
	transform:none;
}

.thumbnails-hover ul{
	-webkit-transform:scale3d(0.6, 0.6, 1);
	-moz-transform:scale3d(0.6, 0.6, 1);
	-o-transform:scale3d(0.6, 0.6, 1);
	-ms-transform:scale3d(0.6, 0.6, 1);
	transform:scale3d(0.6, 0.6, 1);
}

.thumbnails li{
	list-style:none;
	display:inline-block;
	margin:0 5px;
	-webkit-box-shadow:0 0 10px #ccc;
	-moz-box-shadow:0 0 10px #ccc;
	-ms-box-shadow:0 0 10px #ccc;
	-o-box-shadow:0 0 10px #ccc;
	box-shadow:0 0 10px  #ccc;
	-webkit-transition:-webkit-transform 60ms;
	-moz-transition:-webkit-transform 60ms;
	-o-transition:-webkit-transform 60ms;
	-ms-transition:-webkit-transform 60ms;
	transition:-webkit-transform 60ms;
}

.thumbnails li span{
	display:none;
}

.thumbnails .current{
	-webkit-box-shadow:0 0 10px red;
	-moz-box-shadow:0 0 10px red;
	-ms-box-shadow:0 0 10px red;
	-o-box-shadow:0 0 10px red;
	box-shadow:0 0 10px red;
}

.thumbnails .thumb-hover{
	-webkit-transform:scale3d(1.3, 1.3, 1);
	-moz-transform:scale3d(1.3, 1.3, 1);
	-o-transform:scale3d(1.3, 1.3, 1);
	-ms-transform:scale3d(1.3, 1.3, 1);
	transform:scale3d(1.3, 1.3, 1);

	-webkit-box-shadow:0 0 10px #666;
	-moz-box-shadow:0 0 10px #666;
	-ms-box-shadow:0 0 10px #666;
	-o-box-shadow:0 0 10px #666;
	box-shadow:0 0 10px #666;
}

.thumbanils-touch .thumb-hover{
	-webkit-transform:none;
	-moz-transform:none;
	-o-transform:none;
	-ms-transform:none;
	transform:none;
}

.thumbnails .thumb-hover span{
	position:absolute;
	bottom:-30px;
	left:0;
	z-index:2;
	width:100%;
	height:30px;
	font:bold 15px arial;
	line-height:30px;
	color:#666;
	display:block;
	cursor:default;
}

.thumbnails img{
	float:left;
}

.exit-message{
	position: absolute;
	top:10px;
	left:0;
	width:100%;
	height:40px;
	z-index:10000;
}

.exit-message > div{
	width:140px;
	height:30px;
	margin:auto;
	background:rgba(0,0,0,0.5);
	text-align:center;
	font:12px arial;
	line-height:30px;
	color:white;
	-webkit-border-radius:10px;
	-moz-border-radius:10px;
	-ms-border-radius:10px;
	-o-border-radius:10px;
	border-radius:10px;
}

.zoom-icon-in{
	position:absolute;
	z-index:1000;
	width:44px;
	height:44px;
	top:64px;
	left:10px;
	background-position:0 0;
	cursor: pointer;
	background-image:url(../pics/zoom-icons.png);
	background-size:176px 44px;
}

.zoom-icon-out{
	position:absolute;
	z-index:1000;
	width:44px;
	height:44px;
	top:118px;
	left:10px;
	background-position:-88px 0;
	cursor: pointer;
	background-image:url(../pics/zoom-icons.png);
	background-size:176px 44px;
}


.zoom-icon-in.zoom-icon-in-hover{
	background-position:-44px 0;
	cursor: pointer;
}

.zoom-icon-out.zoom-icon-out-hover{
	background-position:-132px 0;
	cursor: pointer;
}

.home-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:10px;
	left:10px;
	background-image:url(../pics/home-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .home-icon:hover{
	background-position:-44px 0;
}

.content-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:226px;
	left:10px;
	background-image:url(../pics/content-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .content-icon:hover{
	background-position:-44px 0;
}

.note-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:280px;
	left:10px;
	background-image:url(../pics/note-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .note-icon:hover{
	background-position:-44px 0;
}

.highlighter-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:334px;
	left:10px;
	background-image:url(../pics/highlighter-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .highlighter-icon:hover{
	background-position:-44px 0;
}

/* Active state for highlighter icon when annotation mode is active */
body.annotation-mode-active .highlighter-icon {
	background-position: -44px 0;
	box-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
	border: 2px solid #ffc107;
}

.bookmark-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:388px;
	left:10px;
	background-image:url(../pics/bookmark-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .bookmark-icon:hover{
	background-position:-44px 0;
}

.test-icon{
	position:absolute;
	z-index:1001;
	width:44px;
	height:44px;
	top:442px;
	left:10px;
	background-image:url(../pics/test-icon.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .test-icon:hover{
	background-position:-44px 0;
}

.page-icon{
	position:absolute;
	z-index:1001;	
	width:44px;
	height:44px;
	top:172px;
	left:10px;
	background-image:url(../pics/page-icons.png);
	background-size:88px 44px;
	background-position:0 0;
	cursor: pointer;
}

html.no-touch .page-icon:hover{
	background-position:-44px 0;
}

.bottom{
	position:absolute;
	left:0;
	bottom:0;
	width:100%;
}

video::-internal-media-controls-download-button {
    display:none;
}

video::-webkit-media-controls-enclosure {
    overflow:hidden;
}

video::-webkit-media-controls-panel {
    width: calc(100% + 30px); /* Adjust as needed */
}


*:not(input):not(textarea) {
  -webkit-user-select: none; /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none; /* disable the IOS popup when long-press on a link */
} 

.plyr__controls {
	border: none !important;
	background: Cornsilk !important;
}

.bookmark-btn {
	height: 30px;
	width: 75px;
	font-size: 12px !important;
}
.bookmark-to-page {
	font-size: 14px !important;
	cursor: pointer;
	margin-bottom: 8px;
}
.bookmark-to-page:hover {
	background-color: orange;
}

.icon-disabled {
	-webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
	filter: grayscale(100%);
}

/* Page Annotator Container */
#page-annotator {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	pointer-events: none; /* Allow clicks to pass through when not active */
}

#page-annotator.active {
	pointer-events: auto; /* Enable interactions when active */
}

/* Ensure the React component container fills the space */
#page-annotator .literally-canvas-container {
	width: 100% !important;
	height: 100% !important;
	border: none !important;
}

/* Visual indicator for annotation mode */
body.annotation-mode-active {
	cursor: crosshair;
}

body.annotation-mode-active .magazine {
	cursor: crosshair !important;
}

/* Disable pointer events on navigation elements during annotation */
body.annotation-mode-active .next-button,
body.annotation-mode-active .previous-button,
body.annotation-mode-active .zoom-icon,
body.annotation-mode-active .page-icon,
body.annotation-mode-active #slider-bar {
	pointer-events: none;
	opacity: 0.3;
}

/* Annotation mode overlay indicator */
body.annotation-mode-active::before {
	content: "画笔模式 - 按ESC键退出";
	position: fixed;
	top: 20px;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(255, 193, 7, 0.9);
	color: #000;
	padding: 8px 16px;
	border-radius: 4px;
	z-index: 10001;
	font-size: 14px;
	font-weight: bold;
	box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* PageAnnotator Toolbar Styles */
.page-annotator-toolbar {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 8px;
	padding: 10px;
	box-shadow: 0 4px 12px rgba(0,0,0,0.3);
	z-index: 10000 !important;
	display: flex !important;
	flex-direction: column !important;
	gap: 5px !important;
}

.annotator-tool-btn {
	border-radius: 4px;
	transition: all 0.2s ease;
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);
	display: block !important;
	width: 50px !important;
	height: 50px !important;
	border: none !important;
	cursor: pointer !important;
}

.annotator-tool-btn:hover {
	transform: scale(1.05);
	box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.annotator-tool-btn:active {
	transform: scale(0.95);
}

/* Specific button styles */
.pen1-btn:hover { box-shadow: 0 4px 8px rgba(255,0,0,0.4); }
.pen2-btn:hover { box-shadow: 0 4px 8px rgba(0,0,255,0.4); }
.pen3-btn:hover { box-shadow: 0 4px 8px rgba(0,255,0,0.4); }
.eraser-btn:hover { box-shadow: 0 4px 8px rgba(128,128,128,0.4); }
.exit-btn:hover { box-shadow: 0 4px 8px rgba(255,68,68,0.4); }