﻿<?php
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
if(!isset($_SESSION['user_session'])) {
	header("Location: /ebookshelf/login.php");
} else {
	if ( $_SESSION['type'] == 1 ) {
		require_once '../../inc/check.user.inc.php';
	} else {
		require_once '../../inc/check.student.inc.php';
	}
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, user-scalable=no" />
	<!-- <meta name="viewport" content="width = 1100, user-scalable = no" /> -->
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="icon" href="favicon.ico" type="image/x-icon">

	<title>電腦及電腦操作的基本概念</title>

	<link rel="stylesheet" href="/min/?g=ebookshelf-css">
	
	<script type="text/javascript" src="/min/?g=ebookshelf-js"></script>
	<script type="text/javascript" src="/min/?g=ebookshelf-js2" async></script>
	<script type="text/javascript" src="/min/?g=ebookshelf-js3"></script>
	
	<script type="text/javascript" src="/min/?g=ebookshelf-te"></script>
	
	</head>
<body>

<div id="canvas">

<div class="home-icon" title="返回書櫃" ></div>

<div class="zoom-icon zoom-icon-in" title="放大"></div>

<div class="zoom-icon zoom-icon-out" title="縮小"></div>

<div class="page-icon page-icon-double" title="單雙頁"></div>

<div class="content-icon" title="目錄"></div>

<div class="note-icon" title="筆記"></div>

<div class="highlighter-icon" title="螢光筆"></div>

<div class="bookmark-icon" title="書簽"></div>

<div class="test-icon" title="測驗結果"></div>

<div class="magazine-viewport">
	<div class="container">
		<div class="magazine">
			<!-- Next button -->
			<div ignore="1" class="next-button"></div>
			<!-- Previous button -->
			<div ignore="1" class="previous-button"></div>
		</div>
	</div>
	<div class="bottom">
		<div id="slider-bar" class="turnjs-slider">
			<div id="slider"></div>
		</div>
	</div>
</div>

<div id="dialogAudio" class="eBookDialog" title="Audio dialog" style="overflow:hidden;" >
	<audio id="myAudio" class="js-player" controls style="width:100%;height:100%;">
	  <source id="mp3_src" src="" type="audio/mpeg">
		Your browser does not support the audio element.
	</audio>
</div>

<div id="dialogVideo" class="eBookDialog" title="Video dialog" style="background-color:black;overflow:hidden;">
	<video id="myVideo" width="100%" height="100%" controls style="background-color:black;">
		<source id="mp4_src" src="" type="video/mp4">
		Your browser does not support the video tag.
	</video>
</div>

<div id="dialogHtml5" class="eBookDialog" title="HTML5 dialog" style="padding:0;overflow:hidden;">
	<iframe id="myHtml5" frameBorder="0" scrolling="no" src="" height="100%" width="100%" ></iframe>
</div>

<div id="dialogNotes" class="eBookDialog" title="Notes dialog" style="overflow:hidden;" >
	<p style="text-align:justify;" ></p>
</div>

<div id="dialogCustomNote" class="eBookDialog" title="Notes dialog" style="overflow:hidden;" >
	<p style="text-align:justify;" ></p>
	<textarea style="width:300px;height:300px;display:hidden;font-size:12px;resize: none;"></textarea>
	<input type="hidden" id="custom-id" >
</div>

<div id="dialogBookmark" class="eBookDialog" title="書簽" style="overflow:hidden;" >
	<div id="bookmark-content" style="height:219px;width:164px;overflow-x:hidden;">
	</div>
	<div style="height:30px;width:164px;overflow:hidden;">
		<button id="bookmark-add" class="bookmark-btn">加入本頁</button> <button  id="bookmark-del" class="bookmark-btn">刪除本頁</button>
	</div>
</div>

<div id="dialogFile" class="eBookDialog" title="上傳檔案" style="overflow:hidden;" >
	<div style="height:75%;overflow-x:hidden;overflow-y:scroll;padding-bottom:20px;">
		<table border="1" style="font-size:12px;border-collapse: collapse;width:100%;">
			<tr>
				<th>id</th><th>檔案名稱</th><th>上傳日期</th><th>插入筆記</th><th>刪除檔案</td>
			</tr>
		</table>
	</div>
	<div style="height:15%;overflow:hidden;font-size:12px;padding-top:20px;">
		<input type="file" id="file-content" accept=".mp3, .pdf" style="border: 1px solid grey;width:80%;"> <button id="file-upload" style="width:15%">上傳</button><br>
		<div style="color:blue;">** 只接受上傳 pdf 檔和 mp3 檔。</div>
		<div class="error" style="color:red;width:100%;text-align:center;"></div>
	</div>
</div>

<div id="dialogTest" class="eBookDialog" title="測驗成績" style="overflow: hidden">
	<div id="testWarning" style="color:red;" ></div>
	<div>班級：<select id="testClass"></select> <button id="testExcel">Export to Excel</button></div>
	<div id="testTable" style="overflow-x: hidden; overflow-y: auto;" >
		<table id="testTable2">
			<tr><th>頁數</th><th>測驗</th><th>名稱</th><th>完成人數</th><th> </th></tr>
			<tr data-page="05" data-number="1"><td class="tableexport-string">P.05</td><td class="tableexport-string">1</td><td class="tableexport-string"><a class="testHtml5">Microsoft Word 界面</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="24" data-number="1"><td class="tableexport-string">P.24</td><td class="tableexport-string">1</td><td class="tableexport-string"><a class="testHtml5">小畫家的功能</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="24" data-number="2"><td class="tableexport-string">P.24</td><td class="tableexport-string">2</td><td class="tableexport-string"><a class="testHtml5">小畫家的功能 2</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="24" data-number="3"><td class="tableexport-string">P.24</td><td class="tableexport-string">3</td><td class="tableexport-string"><a class="testHtml5">小畫家的功能 3</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="40" data-number="1"><td class="tableexport-string">P.40</td><td class="tableexport-string">1</td><td class="tableexport-string"><a class="testHtml5">加入以文本圍繞的圖像</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="53" data-number="1"><td class="tableexport-string">P.53</td><td class="tableexport-string">1</td><td class="tableexport-string"><a class="testHtml5">Microsoft Word 的鍵盤熱鍵</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="56" data-number="1"><td class="tableexport-string">P.56</td><td class="tableexport-string">1</td><td class="tableexport-string"><a class="testHtml5">不同種類的賀卡</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="56" data-number="2"><td class="tableexport-string">P.56</td><td class="tableexport-string">2</td><td class="tableexport-string"><a class="testHtml5">不同種類的賀卡 2</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			<tr data-page="56" data-number="3"><td class="tableexport-string">P.56</td><td class="tableexport-string">3</td><td class="tableexport-string"><a class="testHtml5">不同種類的賀卡 3</a></td><td class="tableexport-string" style="color:blue;">---</td><td class="tableexport-string"><a class="test2">檢視成績</a></td></tr>
			
		</table>
		<br><br>
	</div>
</div>

<div id="dialogTest2" class="eBookDialog" title="測驗成績" style="overflow: hidden">
	<div id="testWarning2" style="color:red;" ></div>
	<div>班級：<select id="testClass2"></select> <button id="testExcel2">Export to Excel</button></div>
	<div id="testTable3" style="overflow-x: hidden; overflow-y: auto;" >
		<table id="testTable4">
			<tr><th>班級</th><th>登入</th><th>名稱</th><th>頁數</th><th>測驗</th><th>得分</th><th></th></tr>
		</table>
		<br><br>
	</div>
	<input type="hidden" id="html5Src" value="">
	<input type="hidden" id="html5Page" value="">
	<input type="hidden" id="html5Number" value="">
</div>

<!-- React PageAnnotator container -->
<div id="page-annotator"></div>

<script type="text/javascript">


$('#canvas').hide();

var pageName = {
	'1':'1',
	'2':'2',
	'3':'3',
	'4':'4',
	'5':'5',
	'6':'6',
	'7':'7',
	'8':'8',
	'9':'9',
	'10':'10',
	'11':'11',
	'12':'12',
	'13':'13',
	'14':'14',
	'15':'15',
	'16':'16',
	'17':'17',
	'18':'18',
	'19':'19',
	'20':'20',
	'21':'21',
	'22':'22',
	'23':'23',
	'24':'24',
	'25':'25',
	'26':'26',
	'27':'27',
	'28':'28',
	'29':'29',
	'30':'30',
	'31':'31',
	'32':'32',
	'33':'33',
	'34':'34',
	'35':'35',
	'36':'36',
	'37':'37',
	'38':'38',
	'39':'39',
	'40':'40',
	'41':'41',
	'42':'42',
	'43':'43',
	'44':'44',
	'45':'45',
	'46':'46',
	'47':'47',
	'48':'48',
};

var players = plyr.setup('.js-player',{volume:5});
var contentPage = 3;
var bookID = 43;
var bookLang = 'chi';
var bookPath = 'cs01-chi';
var disableZoom = false;
var customNoteAdd = false;
var flipBookWidth = 1176;
var flipBookSingleWidth = parseInt( flipBookWidth / 2 );
var flipBookHeight = 780;
var flipBookPages = 48;
var flipBookDisplay = "double";
var zoomMax = 1.5;
var usertype = <?php echo $_SESSION['type']; ?>;


// Load the HTML4 version if there's not CSS transform
yepnope({
	test : Modernizr.csstransforms,
	yep: ['/min/?g=ebookshelf-js4'],
	nope: ['/min/?g=ebookshelf-js5','/min/?g=ebookshelf-2.css'],
	both: ['/min/?g=ebookshelf-js6','/min/?g=ebookshelf-3.css'],
	complete: loadApp
});

</script>

</body>
</html>