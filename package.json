{"name": "ebookshelf-project", "version": "1.0.0", "description": "E-book shelf project with React PageAnnotator component", "private": true, "scripts": {"compile-annotator": "node compile-page-annotator.js", "build": "npm run compile-annotator"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0"}, "keywords": ["ebook", "react", "<PERSON><PERSON><PERSON>", "annotation"], "author": "", "license": "ISC"}