<?php
/**
 * Groups configuration for default Minify implementation
 * @package Minify
 */

/**
 * You may wish to use the Minify URI Builder app to suggest
 * changes. http://yourdomain/min/builder/
 *
 * See http://code.google.com/p/minify/wiki/CustomSource for other ideas
 **/

return array(
    'js' => array('//js/file1.js', '//js/file2.js'),
	'jimp-js' => array(
		'//primary/ebookshelf/js/jquery.min.1.7.js',
		'//primary/ebookshelf/js/jquery-ui-1.8.20.custom.min.js',
		'//primary/ebookshelf/js/jquery.ui.button.js',
		'//primary/ebookshelf/js/jquery.ui.dialog.js',
		'//primary/ebookshelf/js/jquery.ui.resizable.js',
		'//primary/ebookshelf/js/jquery.ui.draggable.js',
		'//primary/ebookshelf/js/jquery.ui.position.js',
		'//primary/ebookshelf/js/jquery.ui.touch-punch.min.js',
		'//primary/ebookshelf/js/modernizr.min.js',
		'//primary/ebookshelf/js/hash.js',
		'//primary/ebookshelf/js/jgestures.min.js',
		'//primary/ebookshelf/js/jquery.mousewheel.min.js',
		'//primary/ebookshelf/js/plyr.js',
		'//primary/ebookshelf/js/Autolinker.min.js',
		'//primary/ebookshelf/js/jquery.modal.min.js',
		'//primary/ebookshelf/js/literallycanvas-core.min.js'
	),
	'jimp-js2' => array(
		'//primary/ebookshelf/js/rangetouch.js'
	),
	'jimp-js3' => array(
		'//primary/ebookshelf/js/loadapp.js'
	),
	'jimp-js4' => array(
		'//primary/ebookshelf/js/turn.js'
	),
	'jimp-js5' => array(
		'//primary/ebookshelf/js/turn.html4.min.js'
	),
	'jimp-js6' => array(
		'//primary/ebookshelf/js/zoom.min.js',
		'//primary/ebookshelf/js/magazine.js'
	),
    'jimp-css' => array(
		'//primary/ebookshelf/css/plyr.css',
		'//primary/ebookshelf/css/jquery.ui.core.css',
		'//primary/ebookshelf/css/jquery.ui.button.css',
		'//primary/ebookshelf/css/jquery.ui.dialog.css',
		'//primary/ebookshelf/css/jquery.ui.resizable.css',
		'//primary/ebookshelf/css/jquery.ui.theme.css',
		'//primary/ebookshelf/css/jquery.modal.css',
		'//primary/ebookshelf/css/literallycanvas.css'
	),
	'jimp-2.css' => array(
		'//primary/ebookshelf/css/jquery.ui.html4.css'
	),
	'jimp-3.css' => array(
		'//primary/ebookshelf/css/jquery.ui.css',
		'//primary/ebookshelf/css/magazine.css'
	),
	'k-css' => array(
		'//ebookshelf/html5/karaoke.css'
	),
	'k2-css' => array(
		'//ebookshelf/html5/karaoke2.css'
	),
	'k-js' => array(
		'//ebookshelf/html5/jquery-1.12.4.min.js',
		'//ebookshelf/html5/karaoke.js'
	),
	'r-css' => array(
		'//ebookshelf/html5/recorder.css'
	),
	'r-js' => array(
		'//ebookshelf/html5/jquery-1.12.4.min.js',
		'//ebookshelf/html5/recorder.js'
	),
	'ntm-js' => array(
		'//newtrendmusic/ebookshelf/js/jquery.min.1.7.js',
		'//newtrendmusic/ebookshelf/js/jquery-ui-1.8.20.custom.min.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.button.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.dialog.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.resizable.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.draggable.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.position.js',
		'//newtrendmusic/ebookshelf/js/jquery.ui.touch-punch.min.js',
		'//newtrendmusic/ebookshelf/js/modernizr.min.js',
		'//newtrendmusic/ebookshelf/js/hash.js',
		'//newtrendmusic/ebookshelf/js/jgestures.min.js',
		'//newtrendmusic/ebookshelf/js/jquery.mousewheel.min.js',
		'//newtrendmusic/ebookshelf/js/plyr.js',
		'//newtrendmusic/ebookshelf/js/Autolinker.min.js',
		'//newtrendmusic/ebookshelf/js/jquery.modal.min.js',
		'//newtrendmusic/ebookshelf/js/literallycanvas-core.min.js'
	),
	'ntm-js2' => array(
		'//newtrendmusic/ebookshelf/js/rangetouch.js'
	),
	'ntm-js3' => array(
		'//newtrendmusic/ebookshelf/js/loadapp.js'
	),
	'ntm-js4' => array(
		'//newtrendmusic/ebookshelf/js/turn.js'
	),
	'ntm-js5' => array(
		'//newtrendmusic/ebookshelf/js/turn.html4.min.js'
	),
	'ntm-js6' => array(
		'//newtrendmusic/ebookshelf/js/zoom.min.js',
		'//newtrendmusic/ebookshelf/js/magazine.js'
	),
    'ntm-css' => array(
		'//newtrendmusic/ebookshelf/css/plyr.css',
		'//newtrendmusic/ebookshelf/css/jquery.ui.core.css',
		'//newtrendmusic/ebookshelf/css/jquery.ui.button.css',
		'//newtrendmusic/ebookshelf/css/jquery.ui.dialog.css',
		'//newtrendmusic/ebookshelf/css/jquery.ui.resizable.css',
		'//newtrendmusic/ebookshelf/css/jquery.ui.theme.css',
		'//newtrendmusic/ebookshelf/css/jquery.modal.css',
		'//newtrendmusic/ebookshelf/css/literallycanvas.css'
	),
	'ntm-2.css' => array(
		'//newtrendmusic/ebookshelf/css/jquery.ui.html4.css'
	),
	'ntm-3.css' => array(
		'//newtrendmusic/ebookshelf/css/jquery.ui.css',
		'//newtrendmusic/ebookshelf/css/magazine.css'
	),
	'ebookshelf-te' => array(
		'//ebookshelf/te/Blob.min.js',
		'//ebookshelf/te/xlsx.core.min.js',
		'//ebookshelf/te/FileSaver.min.js',
		'//ebookshelf/te/tableexport.min.js'
	),
	'ebookshelf-js' => array(
		'//ebookshelf/js/jquery.min.1.7.js',
		'//ebookshelf/js/jquery-ui-1.8.20.custom.min.js',
		'//ebookshelf/js/jquery.ui.button.js',
		'//ebookshelf/js/jquery.ui.dialog.js',
		'//ebookshelf/js/jquery.ui.resizable.js',
		'//ebookshelf/js/jquery.ui.draggable.js',
		'//ebookshelf/js/jquery.ui.position.js',
		'//ebookshelf/js/jquery.ui.touch-punch.min.js',
		'//ebookshelf/js/modernizr.min.js',
		'//ebookshelf/js/hash.js',
		'//ebookshelf/js/jgestures.min.js',
		'//ebookshelf/js/jquery.mousewheel.min.js',
		'//ebookshelf/js/plyr.js',
		'//ebookshelf/js/Autolinker.min.js',
		'//ebookshelf/js/jquery.modal.min.js',
		'//ebookshelf/js/react.production.min.js',
		'//ebookshelf/js/react-dom.production.min.js',
		'//ebookshelf/js/literallycanvas-core.min.js',
		'//ebookshelf/js/literallycanvas.js',
		'//ebookshelf/js/page-annotator.js'
	),
	'ebookshelf-js2' => array(
		'//ebookshelf/js/rangetouch.js'
	),
	'ebookshelf-js3' => array(
		'//ebookshelf/js/loadapp.js'
	),
	'ebookshelf-js4' => array(
		'//ebookshelf/js/turn.js'
	),
	'ebookshelf-js5' => array(
		'//ebookshelf/js/turn.html4.min.js'
	),
	'ebookshelf-js6' => array(
		'//ebookshelf/js/zoom.min.js',
		'//ebookshelf/js/magazine.js'
	),
    'ebookshelf-css' => array(
		'//ebookshelf/css/plyr.css',
		'//ebookshelf/css/jquery.ui.core.css',
		'//ebookshelf/css/jquery.ui.button.css',
		'//ebookshelf/css/jquery.ui.dialog.css',
		'//ebookshelf/css/jquery.ui.resizable.css',
		'//ebookshelf/css/jquery.ui.theme.css',
		'//ebookshelf/css/jquery.modal.css',
		'//ebookshelf/css/literallycanvas.css'
	),
	'ebookshelf-2.css' => array(
		'//ebookshelf/css/jquery.ui.html4.css'
	),
	'ebookshelf-3.css' => array(
		'//ebookshelf/css/jquery.ui.css',
		'//ebookshelf/css/magazine.css'
	),
	'ebookshelf-html5-drag-css' => array(
		'//ebookshelf/html5/html5/drag/css/jquery-ui.css',
		'//ebookshelf/html5/html5/drag/css/index.css'
	),
	'ebookshelf-html5-drag-js' => array(
		'//ebookshelf/html5/html5/drag/js/jquery.js',
		'//ebookshelf/html5/html5/drag/js/jquery-ui.js',
		'//ebookshelf/html5/html5/drag/js/jquery.ui.touch-punch.min.js',
		'//ebookshelf/html5/html5/drag/js/drag.js'
	),
	'ebookshelf-html5-radio-css' => array(
		'//ebookshelf/html5/html5/radio/css/jquery-ui.css',
		'//ebookshelf/html5/html5/radio/css/index.css'
	),
	'ebookshelf-html5-radio-js' => array(
		'//ebookshelf/html5/html5/radio/js/jquery-1.12.3.min.js',
		'//ebookshelf/html5/html5/radio/js/jquery-ui.min.js',
		'//ebookshelf/html5/html5/radio/js/radio.js'
	),
	'ebookshelf-html5-text-css' => array(
		'//ebookshelf/html5/html5/text/css/jquery-ui.css',
		'//ebookshelf/html5/html5/text/css/index.css'
	),
	'ebookshelf-html5-text-js' => array(
		'//ebookshelf/html5/html5/text/js/jquery-1.12.3.min.js',
		'//ebookshelf/html5/html5/text/js/jquery-ui.min.js',
		'//ebookshelf/html5/html5/text/js/text.js'
	),
	'ebookshelf-html5-mc-css' => array(
		'//ebookshelf/html5/html5/mc/css/jquery-ui.css',
		'//ebookshelf/html5/html5/mc/css/index.css'
	),
	'ebookshelf-html5-mc-js' => array(
		'//ebookshelf/html5/html5/mc/js/jquery.js',
		'//ebookshelf/html5/html5/mc/js/jquery-ui.js',
		'//ebookshelf/html5/html5/mc/js/jquery.ui.touch-punch.min.js',
		'//ebookshelf/html5/html5/mc/js/mc.js'
	),
	'ebookshelf-html5-open-css' => array(
		'//ebookshelf/html5/html5/open/css/jquery-ui.css',
		'//ebookshelf/html5/html5/open/css/index.css'
	),
	'ebookshelf-html5-open-js' => array(
		'//ebookshelf/html5/html5/open/js/jquery-1.12.3.min.js',
		'//ebookshelf/html5/html5/open/js/jquery-ui.min.js',
		'//ebookshelf/html5/html5/open/js/open.js'
	),
);
